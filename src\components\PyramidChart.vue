<template>
  <div class="bar-card">
    <div ref="volumn" class="volume" />
  </div>
</template>

<script>
export default {
  name: 'Pyramid<PERSON>hart',
  data() {
    return {
      myChart: null,
    };
  },
  mounted() {
    // 获取数据。
    if (this.$refs.volumn) {
      this.reloadChart();
      // 自适应浏览器。
      window.addEventListener("resize", () => {
        this.reloadChart();
      });
    }
  },
  // 组件销毁。
  beforeUnmount() {
    this.disposeChart();
  },
  methods: {
    drawChart() {
      this.myChart = this.$echarts.init(this.$refs.volumn);
      var data = [85, 15];
      var xData = [
        "个人",
        "单位",
      ];
      var colors = [
        {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          global: false,
          colorStops: [
            {
              offset: 0,
              color: "#1e4a72",
            },
            {
              offset: 0.5,
              color: "#2d6ba3",
            },
            {
              offset: 1,
              color: "#5fb3f0",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          global: false,
          colorStops: [
            {
              offset: 0,
              color: "#0f3a5f",
            },
            {
              offset: 0.5,
              color: "#1e5a8a",
            },
            {
              offset: 1,
              color: "#4da6d9",
            },
          ],
        },
      ];
      
      var renderItem = function(params, api) {
        var yValue = api.value(1);
        var start = api.coord([api.value(0), 0]);
        var end = api.coord([api.value(0), yValue]);
        var style = api.style();

        // 计算锥形的尺寸
        var height = start[1] - end[1];
        var baseWidth = height * 0.8; // 底面宽度
        var x = start[0];
        var y = end[1];

        // 创建3D锥形的多个面
        var topPoint = [x, y]; // 顶点
        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点
        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点
        var bottomBack = [x, start[1] + baseWidth * 0.3]; // 底面后点（营造3D效果）

        var group = {
          type: "group",
          children: [
            // 主面（正面）
            {
              type: "polygon",
              z2: 3,
              shape: {
                points: [topPoint, bottomLeft, bottomRight],
              },
              style: {
                fill: style.fill,
                stroke: 'rgba(255,255,255,0.1)',
                lineWidth: 1,
              },
            },
            // 左侧面（营造3D效果）
            {
              type: "polygon",
              z2: 2,
              shape: {
                points: [topPoint, bottomLeft, bottomBack],
              },
              style: {
                fill: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: style.fill.colorStops[0].color },
                    { offset: 1, color: style.fill.colorStops[2].color }
                  ]
                },
                opacity: 0.7,
                stroke: 'rgba(255,255,255,0.05)',
                lineWidth: 1,
              },
            },
            // 右侧面（营造3D效果）
            {
              type: "polygon",
              z2: 1,
              shape: {
                points: [topPoint, bottomRight, bottomBack],
              },
              style: {
                fill: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: style.fill.colorStops[0].color },
                    { offset: 1, color: style.fill.colorStops[2].color }
                  ]
                },
                opacity: 0.5,
                stroke: 'rgba(255,255,255,0.05)',
                lineWidth: 1,
              },
            },
          ],
        };

        return group;
      };

      // 绘制图表
      var option = {
        title: {
          text: '警情主体类型分布',
          left: 'center',
          top: '5%',
          textStyle: {
            color: '#ffffff',
            fontSize: this.fontSize(0.6),
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: "rgba(9,35,75,0.8)",
          borderColor: "#2187F9",
          borderWidth: 1,
          borderRadius: 8,
          textStyle: {
            color: "#A7EFFF",
            fontSize: this.fontSize(0.4),
          },
          formatter: function(params) {
            return params.name + ': ' + params.value + '%';
          }
        },
        grid: {
          top: "25%",
          left: "10%",
          bottom: "25%",
          right: "10%",
          containLabel: false,
        },
        xAxis: {
          data: xData,
          show: false, // 隐藏X轴
        },
        yAxis: {
          show: false, // 隐藏Y轴
          max: 100,
        },

        series: [
          {
            type: "custom",
            itemStyle: {
              color: function(params) {
                return colors[params.dataIndex];
              },
            },
            label: {
              show: true,
              position: [0, -30],
              color: "#ffffff",
              fontSize: this.fontSize(0.8),
              fontWeight: 'bold',
              formatter: function(params) {
                return params.data + '%';
              },
              align: 'center'
            },
            data: data,
            renderItem: renderItem,
          },
          // 添加底部标签
          {
            type: "custom",
            renderItem: function(params, api) {
              var x = api.coord([params.dataIndex, 0])[0];
              var y = api.coord([params.dataIndex, 0])[1] + 40;

              return {
                type: 'group',
                children: [
                  // 白色虚线
                  {
                    type: 'line',
                    shape: {
                      x1: x,
                      y1: y - 30,
                      x2: x,
                      y2: y - 10
                    },
                    style: {
                      stroke: '#ffffff',
                      lineWidth: 2,
                      lineDash: [5, 5],
                      opacity: 0.8
                    }
                  },
                  // 标签文字
                  {
                    type: 'text',
                    style: {
                      text: xData[params.dataIndex],
                      x: x,
                      y: y,
                      textAlign: 'center',
                      textVerticalAlign: 'top',
                      fill: '#ffffff',
                      fontSize: 16,
                      fontWeight: 'normal'
                    }
                  }
                ]
              };
            },
            data: data,
          },
        ],
      };
      this.myChart.setOption(option);
    },
    // 字体自适应。
    fontSize(res) {
      const clientWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth;
      if (!clientWidth) return;
      const fontSize = 40 * (clientWidth / 1920);
      return res * fontSize;
    },
    // 销毁图表。
    disposeChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
    },
    // 重新加载图表。
    reloadChart() {
      this.disposeChart();
      this.drawChart();
    },
  },
};
</script>

<style scoped>
.bar-card {
  width: 100%;
  height: 100%;
  .volume {
    width: 100%;
    height: 100%;
  }
}
</style>
