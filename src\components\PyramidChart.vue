<template>
  <div class="bar-card">
    <div ref="volumn" class="volume" />
  </div>
</template>

<script>
export default {
  name: 'Pyramid<PERSON>hart',
  data() {
    return {
      myChart: null,
    };
  },
  mounted() {
    // 获取数据。
    if (this.$refs.volumn) {
      this.reloadChart();
      // 自适应浏览器。
      window.addEventListener("resize", () => {
        this.reloadChart();
      });
    }
  },
  // 组件销毁。
  beforeUnmount() {
    this.disposeChart();
  },
  methods: {
    drawChart() {
      this.myChart = this.$echarts.init(this.$refs.volumn);
      var data = [85, 15];
      var xData = [
        "个人",
        "单位",
      ];
      var colors = [
        {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          global: false,
          colorStops: [
            {
              offset: 0,
              color: "#1e4a72",
            },
            {
              offset: 0.5,
              color: "#2d6ba3",
            },
            {
              offset: 1,
              color: "#5fb3f0",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          global: false,
          colorStops: [
            {
              offset: 0,
              color: "#0f3a5f",
            },
            {
              offset: 0.5,
              color: "#1e5a8a",
            },
            {
              offset: 1,
              color: "#4da6d9",
            },
          ],
        },
      ];
      
      var renderItem = function(params, api) {
        var dataIndex = params.dataIndex;
        var value = api.value(1);

        // 调整高度比例，避免差距过大
        var normalizedHeight;
        if (value >= 50) {
          normalizedHeight = 60 + (value - 50) * 0.8; // 大值压缩比例
        } else {
          normalizedHeight = 30 + value * 0.6; // 小值适当放大
        }

        var start = api.coord([dataIndex, 0]);
        var end = api.coord([dataIndex, normalizedHeight]);
        var style = api.style();

        // 计算锥形的尺寸
        var height = start[1] - end[1];
        var baseWidth = height * 0.6;
        var x = start[0];
        var y = end[1];

        // 创建左右两个独立的三角形
        var topPoint = [x, y]; // 顶点
        var bottomCenter = [x, start[1]]; // 底面中心点
        var bottomLeft = [x - baseWidth / 2, start[1]]; // 底面左点
        var bottomRight = [x + baseWidth / 2, start[1]]; // 底面右点

        var group = {
          type: "group",
          children: [
            // 左三角形
            {
              type: "polygon",
              z2: 3,
              shape: {
                points: [topPoint, bottomLeft, bottomCenter],
              },
              style: {
                fill: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: style.fill.colorStops
                },
                stroke: 'rgba(255,255,255,0.2)',
                lineWidth: 1,
              },
            },
            // 右三角形
            {
              type: "polygon",
              z2: 3,
              shape: {
                points: [topPoint, bottomCenter, bottomRight],
              },
              style: {
                fill: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    { offset: 0, color: style.fill.colorStops[0].color },
                    { offset: 0.5, color: style.fill.colorStops[1].color },
                    { offset: 1, color: style.fill.colorStops[2].color }
                  ]
                },
                stroke: 'rgba(255,255,255,0.2)',
                lineWidth: 1,
                opacity: 0.8
              },
            },
            // 中间分割线
            {
              type: "line",
              z2: 4,
              shape: {
                x1: topPoint[0],
                y1: topPoint[1],
                x2: bottomCenter[0],
                y2: bottomCenter[1]
              },
              style: {
                stroke: 'rgba(255,255,255,0.3)',
                lineWidth: 1.5,
              },
            },
          ],
        };

        return group;
      };

      // 绘制图表
      var option = {
        title: {
          text: '警情主体类型分布',
          left: 'center',
          top: '5%',
          textStyle: {
            color: '#ffffff',
            fontSize: this.fontSize(0.6),
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: "rgba(9,35,75,0.8)",
          borderColor: "#2187F9",
          borderWidth: 1,
          borderRadius: 8,
          textStyle: {
            color: "#A7EFFF",
            fontSize: this.fontSize(0.4),
          },
          formatter: function(params) {
            return params.name + ': ' + params.value + '%';
          }
        },
        grid: {
          top: "25%",
          left: "10%",
          bottom: "25%",
          right: "10%",
          containLabel: false,
        },
        xAxis: {
          data: xData,
          show: false, // 隐藏X轴
        },
        yAxis: {
          show: false, // 隐藏Y轴
          max: 120, // 调整最大值以适应新的高度计算
        },

        series: [
          {
            type: "custom",
            itemStyle: {
              color: function(params) {
                return colors[params.dataIndex];
              },
            },
            label: {
              show: true,
              position: [0, -40],
              color: "#ffffff",
              fontSize: this.fontSize(0.8),
              fontWeight: 'bold',
              formatter: function(params) {
                return params.data + '%';
              },
              align: 'center'
            },
            data: data,
            renderItem: renderItem,
          },
          // 添加底部标签
          {
            type: "custom",
            renderItem: function(params, api) {
              var x = api.coord([params.dataIndex, 0])[0];
              var y = api.coord([params.dataIndex, 0])[1] + 40;

              return {
                type: 'group',
                children: [
                  // 白色虚线
                  {
                    type: 'line',
                    shape: {
                      x1: x,
                      y1: y - 30,
                      x2: x,
                      y2: y - 10
                    },
                    style: {
                      stroke: '#ffffff',
                      lineWidth: 2,
                      lineDash: [5, 5],
                      opacity: 0.8
                    }
                  },
                  // 标签文字
                  {
                    type: 'text',
                    style: {
                      text: xData[params.dataIndex],
                      x: x,
                      y: y,
                      textAlign: 'center',
                      textVerticalAlign: 'top',
                      fill: '#ffffff',
                      fontSize: 16,
                      fontWeight: 'normal'
                    }
                  }
                ]
              };
            },
            data: data,
          },
        ],
      };
      this.myChart.setOption(option);
    },
    // 字体自适应。
    fontSize(res) {
      const clientWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth;
      if (!clientWidth) return;
      const fontSize = 40 * (clientWidth / 1920);
      return res * fontSize;
    },
    // 销毁图表。
    disposeChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
    },
    // 重新加载图表。
    reloadChart() {
      this.disposeChart();
      this.drawChart();
    },
  },
};
</script>

<style scoped>
.bar-card {
  width: 100%;
  height: 100%;
  .volume {
    width: 100%;
    height: 100%;
  }
}
</style>
