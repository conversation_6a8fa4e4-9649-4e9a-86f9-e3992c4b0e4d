[{"E:\\1-test\\test-echarts-bar3d\\src\\main.js": "1", "E:\\1-test\\test-echarts-bar3d\\src\\App.vue": "2", "E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue": "3"}, {"size": 214, "mtime": 1756451513338, "results": "4", "hashOfConfig": "5"}, {"size": 1222, "mtime": 1756452126203, "results": "6", "hashOfConfig": "5"}, {"size": 8303, "mtime": 1756452325866, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "27mp38", {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\1-test\\test-echarts-bar3d\\src\\main.js", [], "E:\\1-test\\test-echarts-bar3d\\src\\App.vue", [], "E:\\1-test\\test-echarts-bar3d\\src\\components\\PyramidChart.vue", []]