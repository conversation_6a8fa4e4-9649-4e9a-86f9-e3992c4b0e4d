<template>
  <div class="page-con">
    <div class="main-con">
      <PyramidChart />
    </div>
  </div>
</template>

<script>
import PyramidChart from './components/PyramidChart.vue'

export default {
  name: 'App',
  components: {
    PyramidChart
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  background: #0a1a2e;
}

#app {
  height: 100vh;
  width: 100vw;
}

.page-con {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #0a1a2e 0%, #16213e 30%, #1e3a5f 60%, #2a4a7a 100%);
  position: relative;
  overflow: hidden;
}

.page-con::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(59, 130, 246, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

.main-con {
  width: 90%;
  height: 80%;
  max-width: 1000px;
  max-height: 700px;
  position: relative;
  z-index: 1;
}
</style>
