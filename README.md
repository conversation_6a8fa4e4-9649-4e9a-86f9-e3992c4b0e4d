# ECharts 3D 三棱锥图表项目

这是一个基于 Vue 3 和 ECharts 的3D三棱锥数据可视化项目，实现了类似警情主体类型分布的图表效果。

## 🎯 项目特色

### 视觉效果
- **3D立体锥形**：使用ECharts custom系列创建真实的3D视觉效果
- **垂直渐变**：从顶部深色到底部浅色的自然渐变效果
- **多面体设计**：包含正面、左侧面、右侧面，营造立体感
- **隐藏坐标轴**：干净简洁的图表展示，无显性XY轴
- **科技感背景**：深蓝色渐变背景配合光效

### 交互功能
- **响应式设计**：自适应不同屏幕尺寸
- **悬停提示**：鼠标悬停显示详细数据
- **动态标签**：顶部显示百分比，底部显示类别名称
- **虚线连接**：底部标签与锥形的虚线连接

## 🛠️ 技术栈

- **Vue 3** - 前端框架
- **ECharts 5.4.3** - 图表库
- **JavaScript ES6+** - 编程语言
- **CSS3** - 样式设计

## 📊 图表配置

### 数据结构
```javascript
var data = [85, 15];  // 百分比数据
var xData = ["个人", "单位"];  // 类别标签
```

### 渐变配置
```javascript
// 垂直渐变：从上到下（y: 0 到 y: 1）
{
  type: "linear",
  x: 0, y: 0,
  x2: 0, y2: 1,  // 垂直方向
  colorStops: [
    { offset: 0, color: "#1e4a72" },    // 顶部深色
    { offset: 0.5, color: "#2d6ba3" },  // 中间色
    { offset: 1, color: "#5fb3f0" }     // 底部浅色
  ]
}
```

### 3D效果实现
- **正面**：主要显示面，完整渐变效果
- **左侧面**：透明度0.7，营造阴影效果
- **右侧面**：透明度0.5，增强立体感
- **边框**：白色半透明边框，增强轮廓

## 🚀 运行项目

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run serve
```

### 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
test-echarts-bar3d/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   └── PyramidChart.vue  # 三棱锥图表组件
│   ├── App.vue              # 主应用组件
│   └── main.js              # 入口文件
├── package.json
├── vue.config.js
└── README.md
```

## 🎨 样式特点

- **深蓝色主题**：符合科技感和专业性
- **渐变背景**：多层次的蓝色渐变
- **光效装饰**：径向渐变营造光晕效果
- **字体设计**：白色文字，清晰易读
- **间距布局**：合理的组件间距和比例

## 📈 数据展示

当前展示的是警情主体类型分布：
- **个人**：85%
- **单位**：15%

可以通过修改 `PyramidChart.vue` 中的 `data` 和 `xData` 数组来更改数据内容。

## 🔧 自定义配置

### 修改数据
在 `src/components/PyramidChart.vue` 中：
```javascript
var data = [你的数据1, 你的数据2, ...];
var xData = ["标签1", "标签2", ...];
```

### 修改颜色
调整 `colors` 数组中的 `colorStops` 配置：
```javascript
colorStops: [
  { offset: 0, color: "顶部颜色" },
  { offset: 1, color: "底部颜色" }
]
```

### 修改标题
在 `option` 配置中修改 `title.text` 属性。

## 📝 注意事项

1. 确保数据为数值类型
2. 颜色数组长度应与数据数组长度匹配
3. 字体大小会根据屏幕尺寸自动调整
4. 建议数据不超过5个类别以保持视觉效果

## 🌟 效果预览

项目运行后访问 `http://localhost:8080/` 即可查看3D三棱锥图表效果。
